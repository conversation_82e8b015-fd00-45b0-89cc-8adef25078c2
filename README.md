# DNS Server in C#

[![.NET](https://img.shields.io/badge/.NET-9.0-blueviolet)](https://dotnet.microsoft.com/download/dotnet/9.0)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen)](#build-and-test)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey)](#prerequisites)

> A high-performance DNS server implementation built from scratch in C# using .NET 9.0, featuring RFC 1035 compliance, DNS compression support, and advanced forwarding capabilities.

## Overview

This DNS server is a production-ready implementation built entirely in C# that demonstrates advanced networking concepts and DNS protocol handling. The server supports two operational modes:

1. **Simple Authoritative Mode**: Responds to DNS queries with predefined answers
2. **Forwarding Resolver Mode**: Acts as a recursive resolver by forwarding queries to upstream DNS servers

### Key Capabilities

- **RFC 1035 Compliant**: Full implementation of the DNS protocol specification
- **High Performance**: Asynchronous I/O with minimal memory allocations
- **Production Ready**: Comprehensive error handling, logging, and graceful shutdown
- **Extensible Design**: Clean architecture supporting easy feature additions

## Features

### Core DNS Protocol Support
- ✅ **Complete DNS Packet Parsing**: Header, Question, Answer, Authority, and Additional sections
- ✅ **DNS Compression**: Pointer-based domain name compression (RFC 1035 §4.1.4)
- ✅ **Multiple Record Types**: Extensible support for A, AAAA, CNAME, MX, and other record types
- ✅ **Query/Response Handling**: Proper DNS message flow with error code support

### Network and Performance
- ✅ **Asynchronous Operations**: Non-blocking I/O using modern .NET async patterns
- ✅ **Concurrent Request Handling**: Multiple simultaneous client connections
- ✅ **Memory Efficient**: Uses `Memory<T>`, `Span<T>`, and object pooling
- ✅ **UDP Protocol**: Standard DNS over UDP with 512-byte packet support

### Advanced Features
- ✅ **Query Splitting**: Intelligent handling of multi-question queries in forwarding mode
- ✅ **Response Merging**: Combines multiple upstream responses into single client response
- ✅ **Timeout Management**: Configurable timeouts for upstream DNS queries
- ✅ **Graceful Shutdown**: Clean resource disposal with cancellation token support

### Operational Features
- ✅ **Dual Mode Operation**: Simple authoritative or full forwarding resolver
- ✅ **Command Line Interface**: Easy configuration via CLI arguments
- ✅ **Comprehensive Logging**: Detailed request/response logging for debugging
- ✅ **Error Resilience**: Defensive programming against malformed packets

## Architecture

The server follows a layered architecture with clear separation of concerns:

```mermaid
graph TB
    subgraph "Application Layer"
        A[Program.cs<br/>Entry Point & CLI]
        B[DnsServer.cs<br/>Main Server Logic]
    end

    subgraph "Network Layer"
        C[UDP Socket<br/>Network I/O]
        D[DnsForwarder.cs<br/>Upstream Communication]
    end

    subgraph "Protocol Layer"
        E[DnsPacketParser.cs<br/>Bytes → Objects]
        F[DnsPacketBuilder.cs<br/>Objects → Bytes]
    end

    subgraph "Domain Models"
        G[DnsMessage.cs<br/>Complete DNS Packet]
        H[DnsHeader.cs<br/>DNS Header Fields]
        I[DnsQuestion.cs<br/>Query Section]
        J[DnsAnswer.cs<br/>Response Records]
    end

    A --> B
    B --> C
    B --> D
    B --> E
    B --> F
    E --> G
    F --> G
    G --> H
    G --> I
    G --> J
    D --> E
    D --> F
```

### Component Responsibilities

| Component | Responsibility | Key Features |
|-----------|---------------|--------------|
| **DnsServer** | Main server orchestration | Socket management, request routing, lifecycle |
| **DnsForwarder** | Upstream DNS communication | Query forwarding, response correlation, timeouts |
| **DnsPacketParser** | Deserialization | Binary → Objects, compression handling |
| **DnsPacketBuilder** | Serialization | Objects → Binary, domain name encoding |
| **Protocol Models** | Data representation | Type-safe DNS message structures |

### Design Patterns

- **Async/Await Pattern**: Non-blocking I/O operations throughout
- **Fire-and-Forget**: Request processing doesn't block the main receive loop
- **Producer-Consumer**: Concurrent request handling with task-based processing
- **Factory Pattern**: DNS packet creation and parsing
- **Repository Pattern**: Clean separation between protocol and business logic

## Prerequisites

### System Requirements
- **Operating System**: Windows 10+, Linux (Ubuntu 18.04+), or macOS 10.15+
- **Runtime**: .NET 9.0 SDK or later
- **Memory**: Minimum 512 MB RAM
- **Network**: UDP port 2053 available (configurable)

### Network Permissions
- Ability to bind to UDP port 2053 (may require administrator/root privileges)
- Outbound UDP access to port 53 for forwarding mode

### Verification
```bash
# Verify .NET installation
dotnet --version

# Should output: 9.0.x or later
```

## Installation

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/QuanHoangAnh/DNS-Server-Practice.git
   cd DNS-Server-Practice
   ```

2. **Build the project**
   ```bash
   dotnet build --configuration Release
   ```

3. **Run the server**
   ```bash
   # Simple mode (answers with *******)
   dotnet run

   # Forwarding mode
   dotnet run -- --resolver *******:53
   ```

### Build Configurations

#### Development Build
```bash
dotnet build --configuration Debug
```
- Includes debug symbols
- Verbose logging enabled
- Performance optimizations disabled

#### Production Build
```bash
dotnet build --configuration Release
```
- Optimized for performance
- Minimal logging
- Smaller binary size

#### Self-Contained Deployment
```bash
# Windows
dotnet publish -c Release -r win-x64 --self-contained

# Linux
dotnet publish -c Release -r linux-x64 --self-contained

# macOS
dotnet publish -c Release -r osx-x64 --self-contained
```


## Usage

### Command Line Interface

The server supports the following command-line arguments:

```bash
dotnet run [-- [OPTIONS]]
```

#### Options

| Option | Description | Example | Default |
|--------|-------------|---------|---------|
| `--resolver <endpoint>` | Upstream DNS server | `--resolver *******:53` | None (simple mode) |
| `--port <port>` | Local listening port | `--port 5353` | `2053` |
| `--help` | Show help information | `--help` | - |

#### Examples

```bash
# Simple authoritative mode (default)
dotnet run

# Forward to Google DNS
dotnet run -- --resolver *******:53

# Forward to Cloudflare DNS
dotnet run -- --resolver *******:53

# Forward to custom DNS server
dotnet run -- --resolver ***********:53

# Use custom port
dotnet run -- --port 5353 --resolver *******:53
```

### Operational Modes

#### 1. Simple Authoritative Mode
- **Purpose**: Testing and development
- **Behavior**: Returns `*******` for all A record queries
- **Use Cases**: Local testing, DNS protocol learning
- **Configuration**: No additional arguments required

#### 2. Forwarding Resolver Mode
- **Purpose**: Production DNS resolution
- **Behavior**: Forwards queries to upstream DNS servers
- **Use Cases**: Corporate networks, DNS filtering, caching
- **Configuration**: Requires `--resolver` argument

### Graceful Shutdown

The server supports graceful shutdown via:
- **Ctrl+C** (SIGINT): Stops accepting new connections and completes in-flight requests
- **SIGTERM**: Clean shutdown in containerized environments
- **Process termination**: Automatic resource cleanup

## Configuration

### Environment Variables

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `DNS_PORT` | Listening port | `2053` | `DNS_PORT=5353` |
| `DNS_RESOLVER` | Upstream resolver | None | `DNS_RESOLVER=*******:53` |
| `DNS_TIMEOUT` | Query timeout (ms) | `2000` | `DNS_TIMEOUT=5000` |
| `DNS_LOG_LEVEL` | Logging verbosity | `Info` | `DNS_LOG_LEVEL=Debug` |

### Configuration File Support

Create `appsettings.json` for advanced configuration:

```json
{
  "DnsServer": {
    "ListenPort": 2053,
    "UpstreamResolver": "*******:53",
    "QueryTimeout": 2000,
    "MaxConcurrentQueries": 1000,
    "EnableCompression": true,
    "LogLevel": "Information"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "DnsServer": "Debug"
    }
  }
}
```

### Performance Tuning

#### Memory Settings
```json
{
  "DnsServer": {
    "BufferPoolSize": 1024,
    "MaxPacketSize": 512,
    "PreallocatedBuffers": 100
  }
}
```

#### Network Settings
```json
{
  "DnsServer": {
    "SocketReceiveBufferSize": 65536,
    "SocketSendBufferSize": 65536,
    "ReuseAddress": true
  }
}
```

## Testing

### Unit Testing

The project includes comprehensive unit tests covering:
- DNS packet parsing and building
- Protocol compliance
- Error handling scenarios
- Performance benchmarks

```bash
# Run all tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"

# Run specific test category
dotnet test --filter Category=Integration
```

### Integration Testing

#### Manual Testing with dig

1. **Start the server**
   ```bash
   dotnet run
   ```

2. **Test simple mode**
   ```bash
   dig @127.0.0.1 -p 2053 example.com A
   ```

   Expected output:
   ```
   ;; ANSWER SECTION:
   example.com.    60    IN    A    *******
   ```

3. **Test forwarding mode**
   ```bash
   dotnet run -- --resolver *******:53
   dig @127.0.0.1 -p 2053 google.com A
   ```

   Expected: Real IP addresses for google.com

#### Automated Testing with nslookup

```bash
# Windows
nslookup example.com 127.0.0.1:2053

# Linux/macOS
nslookup example.com 127.0.0.1 -port=2053
```

#### Load Testing

Use `dnsperf` for performance testing:

```bash
# Install dnsperf (Ubuntu/Debian)
sudo apt-get install dnsperf

# Create query file
echo "example.com A" > queries.txt

# Run load test
dnsperf -s 127.0.0.1 -p 2053 -d queries.txt -c 100 -l 30
```

#### Compliance Testing

Test DNS protocol compliance:

```bash
# Test various record types
dig @127.0.0.1 -p 2053 example.com A
dig @127.0.0.1 -p 2053 example.com AAAA
dig @127.0.0.1 -p 2053 example.com MX
dig @127.0.0.1 -p 2053 example.com TXT

# Test EDNS support
dig @127.0.0.1 -p 2053 +edns=0 example.com A

# Test TCP fallback (if implemented)
dig @127.0.0.1 -p 2053 +tcp example.com A
```

### Troubleshooting

#### Common Issues

| Issue | Symptoms | Solution |
|-------|----------|----------|
| Port binding failed | `Address already in use` | Change port or stop conflicting service |
| Permission denied | `Access denied` on port 53 | Run as administrator or use port > 1024 |
| Upstream timeout | No response in forwarding mode | Check upstream DNS server connectivity |
| High memory usage | Memory leaks during load | Enable GC monitoring and check for resource leaks |

#### Debug Mode

Enable verbose logging:

```bash
# Set environment variable
export DNS_LOG_LEVEL=Debug
dotnet run

# Or use configuration
dotnet run -- --log-level Debug
```

#### Network Diagnostics

```bash
# Check port binding
netstat -ulnp | grep 2053

# Monitor DNS traffic
sudo tcpdump -i any port 2053

# Test connectivity
telnet 127.0.0.1 2053
```


## Development

### Project Structure

```
DNS-Server-Practice/
├── DnsServer.csproj          # Project configuration
├── Program.cs                # Application entry point
├── DnsServer.cs              # Main server implementation
├── DnsForwarder.cs           # Forwarding logic
├── Protocol/                 # DNS protocol implementation
│   ├── DnsMessage.cs         # Complete DNS message
│   ├── DnsHeader.cs          # DNS header structure
│   ├── DnsQuestion.cs        # Query section
│   ├── DnsAnswer.cs          # Answer records
│   ├── Building/             # Serialization
│   │   └── DnsPacketBuilder.cs
│   └── Parsing/              # Deserialization
│       └── DnsPacketParser.cs
├── Tests/                    # Unit and integration tests
├── Benchmarks/               # Performance benchmarks
├── Docs/                     # Additional documentation
└── Scripts/                  # Build and deployment scripts
```

### Development Workflow

1. **Setup development environment**
   ```bash
   git clone https://github.com/QuanHoangAnh/DNS-Server-Practice
   cd DNS-Server-Practice
   dotnet restore
   ```

2. **Run in development mode**
   ```bash
   dotnet watch run
   ```

3. **Run tests continuously**
   ```bash
   dotnet watch test
   ```

4. **Code formatting**
   ```bash
   dotnet format
   ```

### Coding Standards

#### C# Style Guidelines
- Follow [Microsoft C# Coding Conventions](https://docs.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions)
- Use `async/await` for all I/O operations
- Implement `IDisposable` for resource management
- Use nullable reference types
- Prefer `ReadOnlySpan<T>` for performance-critical code

#### Architecture Principles
- **Single Responsibility**: Each class has one clear purpose
- **Dependency Injection**: Loose coupling between components
- **Immutable Data**: Prefer immutable objects where possible
- **Error Handling**: Comprehensive exception handling
- **Logging**: Structured logging throughout

### Adding New Features

#### Adding New DNS Record Types

1. **Extend the protocol models**
   ```csharp
   public class DnsMxRecord : DnsAnswer
   {
       public ushort Priority { get; set; }
       public string Exchange { get; set; }
   }
   ```

2. **Update the parser**
   ```csharp
   private DnsAnswer ParseMxRecord()
   {
       // Implementation
   }
   ```

3. **Update the builder**
   ```csharp
   private void BuildMxRecord(DnsMxRecord record)
   {
       // Implementation
   }
   ```

#### Adding Configuration Options

1. **Define configuration model**
   ```csharp
   public class DnsServerOptions
   {
       public int Port { get; set; } = 2053;
       public string? UpstreamResolver { get; set; }
   }
   ```

2. **Register with DI container**
   ```csharp
   services.Configure<DnsServerOptions>(configuration);
   ```

### Debugging

#### Visual Studio / VS Code
- Set breakpoints in DNS packet processing
- Use the debugger to step through packet parsing
- Monitor memory usage during load testing

#### Command Line Debugging
```bash
# Enable debug logging
export DNS_LOG_LEVEL=Debug

# Run with debugger attached
dotnet run --configuration Debug
```

#### Network Debugging
```bash
# Capture DNS packets
sudo tcpdump -i any -w dns_capture.pcap port 2053

# Analyze with Wireshark
wireshark dns_capture.pcap
```